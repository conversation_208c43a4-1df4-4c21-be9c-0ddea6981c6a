using Vsp.PayrollConfiguration.Domain.AbpFund.Interfaces;
using Vsp.PayrollConfiguration.Domain.AbpFund.Models;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Services;

public class AbpFundService(
    IFilteredQuery<AbpFundModel, Repository.Entities.AbpFund, Repository.Entities.Year, ILoketContext> getQuery,
    IInsertInheritanceEntityCommand<AbpFundPostModel, AbpFundModel, Repository.Entities.AbpFund, ModelAbpFund> insertCommand,
    IPatchInheritanceEntityCommand<AbpFundPatchModel, AbpFundModel, Repository.Entities.AbpFund, ModelAbpFund> patchCommand,
    IDeleteInheritanceEntityCommand<ModelAbpFund> deleteCommand,
    ICodeTableHelper codeTableHelper,
    IMapper mapper)
    : IAbpFundService
{
    private readonly IFilteredQuery<AbpFundModel, Repository.Entities.AbpFund, Repository.Entities.Year, ILoketContext> getQuery = getQuery;
    private readonly IInsertInheritanceEntityCommand<AbpFundPostModel, AbpFundModel, Repository.Entities.AbpFund, ModelAbpFund> insertCommand = insertCommand;
    private readonly IPatchInheritanceEntityCommand<AbpFundPatchModel, AbpFundModel, Repository.Entities.AbpFund, ModelAbpFund> patchCommand = patchCommand;
    private readonly IDeleteInheritanceEntityCommand<ModelAbpFund> deleteCommand = deleteCommand;
    private readonly ICodeTableHelper codeTableHelper = codeTableHelper;
    private readonly IMapper mapper = mapper;

    public async Task<IListOperationResult<AbpFundModel>> GetAbpFundsByYearIdAsync(Guid yearId) =>
        await this.getQuery.ExecuteList(yearId);

    public async Task<IOperationResult<AbpFundModel>> PostAbpFundByInheritanceLevelIdAsync(Guid inheritanceLevelId, AbpFundPostModel postModel)
    {
        throw new NotImplementedException();
        // postModel.InheritanceLevelId = inheritanceLevelId;
        // return await this.insertCommand.ExecuteAsync(postModel);
    }

    public async Task<IOperationResult<AbpFundModel>> PatchAbpFundByAbpFundIdAsync(Guid abpFundId, AbpFundPatchModel patchModel)
    {
        throw new NotImplementedException();
        // patchModel.Id = abpFundId;
        // return await this.patchCommand.ExecuteAsync(patchModel);
    }

    public async Task<IOperationResult<NoResult>> DeleteAbpFundByAbpFundIdAsync(Guid abpFundId)
    {
        throw new NotImplementedException();
        // return await this.deleteCommand.ExecuteAsync(abpFundId);
    }

    public async Task<IOperationResult<AbpFundMetadataModel>> GetAbpFundMetadataByProviderIdAsync() =>
        new OperationResult<AbpFundMetadataModel>(
            new AbpFundMetadataModel
            {
                Key = this.mapper.Map<List<KeyValueModel>>(await this.codeTableHelper.GetOptions<CtAbpFund>())
            });

    public Task<IListOperationResult<PayrollComponentModel>> GetLinkedPayrollComponentsByAbpFundIdAsync(Guid abpFundId) =>
        throw new NotImplementedException();
}
