using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Mappers;

public class BaseForCalculationAgeBasedMaximumProfile : Profile
{
    public BaseForCalculationAgeBasedMaximumProfile()
    {
        #region GET

        CreateMap<Repository.Entities.BaseForCalculationAgeBasedMaximum, BaseForCalculationAgeBasedMaximumModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.MapFrom(src => src.InheritanceLevel))
            .ForMember(dst => dst.BaseForCalculation, opt => opt.MapFrom(src => src.BaseForCalculationId))
            .ForMember(dst => dst.Age, opt => opt.MapFrom(src => src.Age))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.StartPayrollPeriod, opt => opt.MapFrom(src => src.PayrollPeriod))
            .ForMember(dst => dst.Maximum, opt => opt.MapFrom(src => src.Maximum))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src));

        CreateMap<Repository.Entities.BaseForCalculationAgeBasedMaximum, BaseForCalculationAgeBasedMaximumModel.BaseForCalculationAgeBasedMaximumDefinedAtLevelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.AgeDefinedAtLevel))
            .ForMember(dst => dst.Maximum, opt => opt.MapFrom(src => src.MaximumDefinedAtLevel));

        #endregion

        #region POST

        // For cloning objects with AutoMapper
        CreateMap<ModelBaseForCalculationAgeBasedMaximum, ModelBaseForCalculationAgeBasedMaximum>()
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore());

        #endregion
        
        #region PATCH

        CreateMap<BaseForCalculationAgeBasedMaximumPatchModel, Repository.Entities.BaseForCalculationAgeBasedMaximum>()
            .ForMember(dst => dst.Maximum, opt => opt.MapFrom(src => src.Maximum))

            // Ignore the primary key properties as they are not needed for PATCH operations
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.YearId, opt => opt.Ignore())
            .ForMember(dst => dst.BaseForCalculationId, opt => opt.Ignore())
            .ForMember(dst => dst.Age, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.Ignore())

            // Ignore the DefinedAtLevel properties as they are not needed for PATCH operations
            .ForMember(dst => dst.AgeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.MaximumDefinedAtLevel, opt => opt.Ignore())

            // Ignore the navigation properties as they are not needed for PATCH operations
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore());

        CreateMap<Repository.Entities.BaseForCalculationAgeBasedMaximum, ModelBaseForCalculationAgeBasedMaximum>()
            .ForMember(dst => dst.Maximum, opt => opt.MapFrom(src => src.Maximum))

            // Mapping the primary key properties
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom(src => src.InheritanceLevelId))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.BaseForCalculationId, opt => opt.MapFrom(src => src.BaseForCalculationId))
            .ForMember(dst => dst.Age, opt => opt.MapFrom(src => src.Age))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.PayrollPeriodId))

            // Ignore the navigation properties as they are not needed for PATCH operations
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore());

        #endregion
    }
}