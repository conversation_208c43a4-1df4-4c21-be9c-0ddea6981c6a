using AutoMapper.QueryableExtensions;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Validators;

internal class PatchPayrollComponentValidator : InitializableAbstractValidator<PayrollComponentPatchModel, PatchPayrollComponentValidationInfo>
{
    private readonly ILoketContext loketContext;
    private readonly IMapper mapper;
    private readonly ICodeTableHelper codeTableHelper;
    private bool showWarnings = true;

    private static readonly HashSet<int> FundEmploymentContributionComponents =
    [
        133, 134, 135, 381, 500, 505, 510, 515, 520, 548, 555, 562, 569, 576, 583, 590, 597, 604, 611, 618, 774, 781,
        788, 795, 802, 2001, 2008, 2015, 2022, 2029, 2036, 2043, 2050, 2057, 2064
    ];

    private static readonly HashSet<int> FundTotalContributionComponents =
    [
        253, 254, 248, 382, 503, 508, 513, 518, 523, 551, 558, 565, 572, 579, 586, 593, 600, 607, 614, 621, 777, 784,
        791, 798, 805, 2004, 2011, 2018, 2025, 2032, 2039, 2046, 2053, 2060, 2067
    ];

    private static readonly HashSet<int> BaseForCalculationResultComponents =
    [
        82, 83, 84, 85, 681, 682, 683, 684, 685, 686, 868, 869, 870, 871, 872, 3001, 3017, 3033, 3049, 3065, 3081, 3097, 3113, 3129, 3145
    ];

    public PatchPayrollComponentValidator(ILoketContext loketContext, IMapper mapper, ICodeTableHelper codeTableHelper)
    {
        this.loketContext = loketContext;
        this.mapper = mapper;
        this.codeTableHelper = codeTableHelper;

        // Stage 1 validations: Code table validation rules
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidCodeTableAsync<CtDeductionOrPayment>(x.DeductionOrPayment, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_Invalid)
            .WithMessage("deductionOrPayment.key is invalid");
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidCodeTableAsync<CtPaymentPeriod>(x.PaymentPeriod, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_PaymentPeriod_Invalid)
            .WithMessage("paymentPeriod.key is invalid");
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidCodeTableAsync<CtTaxLiable>(x.TaxLiable, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_TaxLiable_Invalid)
            .WithMessage("taxLiable.key is invalid");
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidCodeTableAsync<CtSocialSecurityLiable>(x.SocialSecurityLiable, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_Invalid)
            .WithMessage("socialSecurityLiable.key is invalid");
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidCodeTableAsync<CtHoursIndication>(x.HoursIndication, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_HoursIndication_Invalid)
            .WithMessage("hoursIndication.key is invalid");
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidCodeTableAsync<CtCostsEmployer>(x.CostsEmployer, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_CostsEmployer_Invalid)
            .WithMessage("costsEmployer.key is invalid");
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidCodeTableAsync<CtBaseForCalculationBter>(x.BaseForCalculationBter, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_BaseForCalculationBter_Invalid)
            .WithMessage("baseForCalculationBter.key is invalid");

        // Stage 2 validations: Component specific validation rules (only do the check if all code table validations pass)
        When(x => x.Key != -1, () =>
            {
                // Rule #0: Combination of DeductionOrPayment, TaxLiable, and SocialSecurityLiable is invalid
                RuleFor(x => x)
                    .Must(x => !PayrollComponent_Column_1(x))
                    .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Column_1)
                    .WithMessage("Combination of deductionOrPayment, taxLiable, and socialSecurityLiable is invalid.")
                    .DependentRules(() =>
                    {
                        // Rule #1: DeductionOrPayment may NOT be changed for this component UNLESS:
                        //   - the component is a special component, or
                        //   - the component is in category 30 or 11 (special categories)
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_DeductionOrPayment_1(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_1)
                            .WithMessage("deductionOrPayment may not be changed for this component");

                        // Rule #2: DeductionOrPayment may NOT be changed for this component UNLESS there are NO payroll period results for this component within the year.
                        RuleFor(x => x)
                            .MustAsync(async (x, cancellation) => !await PayrollComponent_DeductionOrPayment_2(x, cancellation))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_2)
                            .WithMessage("deductionOrPayment may not be changed for this component. There are payroll period results for this component within the year.");

                        // Rule #3: TaxLiable may NOT be changed for this component UNLESS:
                        //   - the component is a special component, or
                        //   - the component is in category 30 or 11 (special categories)
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_TaxLiable_1(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_TaxLiable_1)
                            .WithMessage("taxLiable may not be changed for this component.");

                        // Rule #4: SocialSecurityLiable may NOT be changed for this component UNLESS:
                        //   - the component is a special component, or
                        //   - the component is in category 30 or 11 (special categories)
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_SocialSecurityLiable_1(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_1)
                            .WithMessage("socialSecurityLiable may not be changed for this component.");

                        // Rule #5: PaymentPeriod is invalid if value is 5 or 6 and Key is not between 101 and 120
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_PaymentPeriod_1(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_PaymentPeriod_1)
                            .WithMessage("paymentPeriod is invalid.");

                        // Rule #6: IsPayment may NOT be true for this component unless Key is 260/479, DeductionOrPayment is 2, or (Column is 0 and BalanceSheetSide is 1)
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_IsPayment_1(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_IsPayment_1)
                            .WithMessage("isPayment may not be true for this component.");

                        // Rule #7: IsPayment may not be false for net wage components and employee savings scheme components.
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_IsPayment_2(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_IsPayment_2)
                            .WithMessage(
                                "isPayment may not be false for net wage components and employee savings scheme components.");

                        // Rule #8: PaymentDescription may NOT be filled when IsPayment is false
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_PaymentDescription_1(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_PaymentDescription_1)
                            .WithMessage("paymentDescription may not be filled when isPayment is false.");

                        // Rule #9: CostsEmployer must be empty if component is 495
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_CostsEmployer_1(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_CostsEmployer_1)
                            .WithMessage("costsEmployer must be empty for this component.");

                        // Rule #10: CostsEmployer must not be negative (-) when the component is a payment (deductionOrPayment 1).
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_CostsEmployer_2(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_CostsEmployer_2)
                            .WithMessage("costsEmployer may not be negative (-) when the component is a payment (deductionOrPayment 1).");

                        // Rule #11: CostsEmployer must not be positive (+) when the component is a deduction (DeductionOrPayment 2)
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_CostsEmployer_3(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_CostsEmployer_3)
                            .WithMessage("costsEmployer may not be positive (+) when the component is a deduction (deductionOrPayment 2).");

                        // Rule #12: IsNetToGross may NOT be true UNLESS:
                        //   - DeductionOrPayment is 1
                        //   *AND*
                        //   - TaxLiable is 1 or 3
                        //   *AND*
                        //   - Category is 7, 8, or 30
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_IsNetToGross_1(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_IsNetToGross_1)
                            .WithMessage("isNetToGross may not be true in combination with the current deductionOrPayment, taxLiable and category values.");

                        // Rule #13: IsNetToGross may NOT be true when IsBaseForCalculationOvertime is true
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_IsNetToGross_2(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_IsNetToGross_2)
                            .WithMessage("isNetToGross may not be true when isBaseForCalculationOvertime is true.");

                        // Rule #14: IsNetToGross may NOT be true when IsBaseForCalculationDailyWageSupplement is true
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_IsNetToGross_3(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_IsNetToGross_3)
                            .WithMessage("isNetToGross may not be true when isBaseForCalculationDailyWageSupplement is true.");

                        // Rule #15: IsNetToGross may NOT be true when IsBaseForCalculationDailyWageZw is true
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_IsNetToGross_4(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_IsNetToGross_4)
                            .WithMessage("isNetToGross may not be true when isBaseForCalculationDailyWageZw is true.");

                        // Rule #16: IsNetToGross may NOT be true when the component is a base for BTER calculation using fixed employee data
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_IsNetToGross_5(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_IsNetToGross_5)
                            .WithMessage("isNetToGross may not be true when the component is a base for BTER calculation using fixed employee data.");

                        // Rule #17: IsOvertime must be true for overtime and part-time components
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_IsOvertime_1(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_IsOvertime_1)
                            .WithMessage("isOvertime must be true for overtime and part-time components.");

                        // Rule #18: IsOvertime may only be true for components that are payments, tax liable, and social security liable
                        RuleFor(x => x)
                            .Must(x => !PayrollComponent_IsOvertime_2(x))
                            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_IsOvertime_2)
                            .WithMessage("isOvertime may only be true for components that are payments, tax liable, and social security liable.");
                    });
            });

        // Stage 3 validations: Warnings for specific conditions
        When(x => this.showWarnings, () =>
        {
            // Warning #1: socialSecurityLiable is empty while taxLiable is 1, 2, 3 or 4
            RuleFor(x => x)
                .Must(x => !PayrollComponent_SocialSecurityLiable_2(x))
                .WithSeverity(Severity.Warning)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_2)
                .WithMessage("socialSecurityLiable is empty while taxLiable is 1, 2, 3 or 4.");
        });
    }

    #region Code table validation methods

    private async Task<bool> BeValidCodeTableAsync<TCodeTable>(
        KeyModel? value,
        PayrollComponentPatchModel? model,
        CancellationToken _)
        where TCodeTable : CodeTable
    {
        var isValid = value == null || (await this.codeTableHelper.GetOptions<TCodeTable>()).Any(x => x.Key == value.Key);
        if (!isValid)
            model!.Key = -1;
        return isValid;
    }

    #endregion

    #region Component specific validation methods

    private bool PayrollComponent_Column_1(PayrollComponentPatchModel model) =>
        (model.DeductionOrPayment is not null || model.TaxLiable is not null || model.SocialSecurityLiable is not null)
        && model.Column?.Key == 0
        && !(model.DeductionOrPayment is null
             && new int?[] { 5, 6, 7, 8, 9, 10, 11, 12 }.Contains(model.TaxLiable?.Key)
             && model.SocialSecurityLiable is null);

    private bool PayrollComponent_DeductionOrPayment_1(PayrollComponentPatchModel model)
    {
        var result = IsDeductionOrPaymentChanged(model) && IsNotSpecialComponentOrCategory(model);
        if (result) this.showWarnings = false;
        return result;
    }

    private async Task<bool> PayrollComponent_DeductionOrPayment_2(PayrollComponentPatchModel model, CancellationToken cancellationToken)
    {
        var result = IsDeductionOrPaymentChanged(model) && await HasPayrollPeriodResultsAsync(cancellationToken);
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_TaxLiable_1(PayrollComponentPatchModel model)
    {
        var result = IsTaxLiableChanged(model) && IsNotSpecialComponentOrCategory(model);
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_SocialSecurityLiable_1(PayrollComponentPatchModel model)
    {
        var result = IsSocialSecurityLiableChanged(model) && IsNotSpecialComponentOrCategory(model);
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_PaymentPeriod_1(PayrollComponentPatchModel model)
    {
        var result = model is { PaymentPeriod.Key: 5 or 6, Key: < 101 or > 120 };
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_IsPayment_1(PayrollComponentPatchModel model)
    {
        var result = model
            is { IsPayment: true }
            and not ({ Key: 260 or 479 } or { DeductionOrPayment.Key: 2 } or { Column.Key: 0, BalanceSheetSide.Key: 1 });
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_IsPayment_2(PayrollComponentPatchModel model)
    {
        var result = model
                is { IsPayment: false }
            and { Key: 260 or 479 };
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_PaymentDescription_1(PayrollComponentPatchModel model)
    {
        var result = model.IsPayment is false && !string.IsNullOrEmpty(model.PaymentDescription);
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_CostsEmployer_1(PayrollComponentPatchModel model)
    {
        var result = model is { Key: 495, CostsEmployer: not null };
        //model.Key is 495 and model.CostsEmployer is not null;
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_CostsEmployer_2(PayrollComponentPatchModel model)
    {
        var result = model.DeductionOrPayment?.Key == 1 && model.CostsEmployer?.Key == -1;
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_CostsEmployer_3(PayrollComponentPatchModel model)
    {
        var result = model.DeductionOrPayment?.Key == 2 && model.CostsEmployer?.Key == 1;
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_IsNetToGross_1(PayrollComponentPatchModel model)
    {
        var result = model.IsNetToGross is true &&
                     !(model.DeductionOrPayment?.Key is 1 &&
                       model.TaxLiable?.Key is 1 or 3 &&
                       this.ValidationInformation.PayrollComponentModel.Category.Key is 7 or 8 or 30);
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_IsNetToGross_2(PayrollComponentPatchModel model)
    {
        var result = model.IsNetToGross is true && model.IsBaseForCalculationOvertime == true;
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_IsNetToGross_3(PayrollComponentPatchModel model)
    {
        var result = model.IsNetToGross is true && model.IsBaseForCalculationDailyWageSupplement == true;
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_IsNetToGross_4(PayrollComponentPatchModel model)
    {
        var result = model.IsNetToGross is true && model.IsBaseForCalculationDailyWageZw == true;
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_IsNetToGross_5(PayrollComponentPatchModel model)
    {
        var result = model.IsNetToGross is true && model.BaseForCalculationBter?.Key == 2;
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_IsOvertime_1(PayrollComponentPatchModel model)
    {
        var result = model.Key is 73 or 74 or 4041 or 4042 && model.IsOvertime == false;
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_IsOvertime_2(PayrollComponentPatchModel model)
    {
        var result = model.IsOvertime == true &&
                     model is not
                     {
                         DeductionOrPayment.Key: 1,
                         SocialSecurityLiable.Key: 1,
                         TaxLiable.Key: 1 or 3
                     };
        if (result) this.showWarnings = false;
        return result;
    }

    private bool PayrollComponent_SocialSecurityLiable_2(PayrollComponentPatchModel model) =>
        model.SocialSecurityLiable is null
        && (this.ValidationInformation.PayrollComponentModel?.Year ?? model.Year!.Value) >= 2013
        && model.TaxLiable is { Key: 1 or 2 or 3 or 4 };

    private bool IsNotSpecialComponentOrCategory(PayrollComponentPatchModel model) =>
        !FundEmploymentContributionComponents.Contains(model.Key)
        && !FundTotalContributionComponents.Contains(model.Key)
        && !BaseForCalculationResultComponents.Contains(model.Key)
        && this.ValidationInformation.PayrollComponentModel.Category.Key != 30
        && this.ValidationInformation.PayrollComponentModel.Category.Key != 11;

    private async Task<bool> HasPayrollPeriodResultsAsync(CancellationToken cancellationToken)
    {
        var payrollComponentModel = this.ValidationInformation.PayrollComponentModel;

        var query = this.loketContext.Set<EmployeeWageResultComponent>()
            .AsNoTracking()
            .Where(x =>
                x.InheritanceLevel.Id == payrollComponentModel.InheritanceLevel.Id &&
                x.YearId == payrollComponentModel.Year &&
                x.ComponentId == payrollComponentModel.Key!.Value);

        return await query.AnyAsync(cancellationToken);
    }

    private bool IsDeductionOrPaymentChanged(PayrollComponentPatchModel model) =>
        this.ValidationInformation.PayrollComponentModel.DeductionOrPayment?.Key != model.DeductionOrPayment?.Key;

    private bool IsTaxLiableChanged(PayrollComponentPatchModel model) =>
        this.ValidationInformation.PayrollComponentModel?.TaxLiable?.Key != model.TaxLiable?.Key;

    private bool IsSocialSecurityLiableChanged(PayrollComponentPatchModel model) =>
        this.ValidationInformation.PayrollComponentModel?.SocialSecurityLiable?.Key != model.SocialSecurityLiable?.Key;

    #endregion


    protected override async Task<PatchPayrollComponentValidationInfo> Initialize(PayrollComponentPatchModel model)
    {
        // Initialize the validation information with a new PayrollComponentModel, either fetched from the database or created as a new instance
        var payrollComponentModel = await GetCurrentComponentModel(model) ?? new PayrollComponentModel
        {
            InheritanceLevel = new InheritanceLevelModel { Id = model.InheritanceLevelId, },
            Year = model.Year!.Value,
            Key = model.Key,
            DeductionOrPayment = model.DeductionOrPayment != null ? new KeyValueModel { Key = model.DeductionOrPayment.Key, Value = null } : null,
            TaxLiable = model.TaxLiable != null ? new KeyValueModel { Key = model.TaxLiable.Key, Value = null } : null,
            SocialSecurityLiable = model.SocialSecurityLiable != null ? new KeyValueModel { Key = model.SocialSecurityLiable.Key, Value = null } : null,
            Category = new KeyValueModel { Key = model.Category?.Key, Value = null }
        };

        return new PatchPayrollComponentValidationInfo { PayrollComponentModel = payrollComponentModel };
    }


    private async Task<PayrollComponentModel?> GetCurrentComponentModel(PayrollComponentPatchModel model) =>
        await this.loketContext.Set<Component>()
            .AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Component>(model.Id))
            .ProjectTo<PayrollComponentModel>(this.mapper.ConfigurationProvider)
            .SingleOrDefaultAsync();
}

public class PatchPayrollComponentValidationInfo
{
    public PayrollComponentModel PayrollComponentModel { get; set; } = null!;
}
