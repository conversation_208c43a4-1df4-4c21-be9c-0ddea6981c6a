using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Queries;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Validators;

internal class InsertPayrollComponentValidator : AbstractValidator<PayrollComponentPostModel>
{
    private readonly ILoketContext loketContext;
    private readonly GetAvailablePayrollComponentsQuery availableComponentsQuery;

    public InsertPayrollComponentValidator(ILoketContext loketContext, IMapper mapper, GetAvailablePayrollComponentsQuery availableComponentsQuery)
    {
        this.loketContext = loketContext;
        this.availableComponentsQuery = availableComponentsQuery;

        Include(new InsertInheritanceEntityValidator<PayrollComponentPostModel, Component, ModelComponent>(loket<PERSON>ontext, mapper));

        RuleFor(x => x)
            .MustAsync(BeValidPayrollComponentAsync)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Post_Invalid)
            .WithMessage("Given payroll component is invalid. It is either unknown, or already exist on the given year of the inheritance level.");
    }

    private async Task<bool> BeValidPayrollComponentAsync(PayrollComponentPostModel postModel, CancellationToken token)
    {
        var year = await this.loketContext.Years.AsNoTracking()
            .Where(y => y.InheritanceLevel.Id == postModel.InheritanceLevelId && y.YearId == postModel.Year!.Value)
            .SingleOrDefaultAsync(token);
        if (year == null) return true;

        var predicate = this.availableComponentsQuery.FilterCollectionByExpression(year.Id);

        var componentIsAvailable = await this.loketContext.Set<ComponentGeneral>()
            .Where(predicate!)
            .Where(cg => cg.ComponentId == postModel.Key)
            .AnyAsync(token);
        return componentIsAvailable;
    }
}