using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using Vsp.PayrollConfiguration.Infrastructure.Constants;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Validators;

internal class InsertBaseForCalculationBasePayrollComponentValidator
    : AbstractValidator<BaseForCalculationBasePayrollComponentPostModel>
{
    private readonly ILoketContext loketContext;
    private readonly IMapper mapper;

    public InsertBaseForCalculationBasePayrollComponentValidator(
        ILoketContext loketContext,
        IMapper mapper,
        IBaseForCalculationBasePayrollComponentHelper baseForCalculationBasePayrollComponentHelper
    )
    {
        this.loketContext = loketContext;
        this.mapper = mapper;

        // Stage 1: Include inheritance validations (including AlreadyExists checks)
        // These are more specific validations and should run first
        Include(new InsertInheritanceEntityValidator<
            BaseForCalculationBasePayrollComponentPostModel,
            Repository.Entities.BaseForCalculationBasePayrollComponent,
            ModelBaseForCalculationBasePayrollComponent>(loketContext, mapper));

        // Stage 2: PayrollComponent validation - runs only if inheritance validations pass
        // This prevents the less specific PayrollComponent_Invalid error from masking
        // the more specific AlreadyExists_CurrentInheritanceLevel error
        WhenAsync(async (model, token) => await InheritanceValidationsPassAsync(model, token), () =>
        {
            RuleFor(m => m.PayrollComponent.Key)
                .MustAsync(async (model, componentId, token) =>
                {
                    return await baseForCalculationBasePayrollComponentHelper
                        .GetAvailableBasePayrollComponents(model.BaseForCalculationGuidId, model.StartPayrollPeriod.PeriodNumber!.Value)
                        .Where(c => c.ComponentId == componentId)
                        .AnyAsync(token);
                })
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollComponent_Invalid)
                .WithMessage("payrollComponent.key is invalid");
        });
    }

    private async Task<bool> InheritanceValidationsPassAsync(BaseForCalculationBasePayrollComponentPostModel model, CancellationToken token)
    {
        // Create a temporary validator to check if inheritance validations pass
        var inheritanceValidator = new InsertInheritanceEntityValidator<
            BaseForCalculationBasePayrollComponentPostModel,
            Repository.Entities.BaseForCalculationBasePayrollComponent,
            ModelBaseForCalculationBasePayrollComponent>(loketContext, mapper);

        var result = await inheritanceValidator.ValidateAsync(model, token);
        return result.IsValid;
    }
}