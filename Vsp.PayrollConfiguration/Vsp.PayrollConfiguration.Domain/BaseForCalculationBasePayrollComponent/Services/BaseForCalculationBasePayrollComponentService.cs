using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Services;

public class BaseForCalculationBasePayrollComponentService(
    IGetInheritanceEntityQuery<BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent> getQuery,
    IInsertInheritanceEntityCommand<BaseForCalculationBasePayrollComponentPostModel, BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent> insertCommand,
    IPatchInheritanceEntityCommand<BaseForCalculationBasePayrollComponentPatchModel, BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent> patchCommand,
    IDeleteInheritanceEntityCommand<ModelBaseForCalculationBasePayrollComponent> deleteCommand,
    IGetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery getAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery,
    ICodeTableHelper codeTableHelper,
    IBaseForCalculationBasePayrollComponentHelper baseForCalculationBasePayrollComponentHelper) : IBaseForCalculationBasePayrollComponentService
{
    private readonly IGetInheritanceEntityQuery<BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent> getQuery = getQuery;
    private readonly IInsertInheritanceEntityCommand<BaseForCalculationBasePayrollComponentPostModel, BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent> insertCommand = insertCommand;
    private readonly IPatchInheritanceEntityCommand<BaseForCalculationBasePayrollComponentPatchModel, BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent> patchCommand = patchCommand;
    private readonly IDeleteInheritanceEntityCommand<ModelBaseForCalculationBasePayrollComponent> deleteCommand = deleteCommand;
    private readonly IGetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery getAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery = getAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery;
    private readonly ICodeTableHelper codeTableHelper = codeTableHelper;
    private readonly IBaseForCalculationBasePayrollComponentHelper baseForCalculationBasePayrollComponentHelper = baseForCalculationBasePayrollComponentHelper;

    public async Task<IListOperationResult<BaseForCalculationBasePayrollComponentModel>>
        GetBaseForCalculationBasePayrollComponentsByBaseForCalculationIdAsync(Guid baseForCalculationId) =>
        await this.getQuery.ExecuteListAsync(baseForCalculationId);

    public async Task<IOperationResult<BaseForCalculationBasePayrollComponentModel>>
        PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync(
            Guid baseForCalculationId,
            BaseForCalculationBasePayrollComponentPostModel postModel)
    {
        var baseForCalculation = await this.baseForCalculationBasePayrollComponentHelper.GetBaseForCalculationWithInheritanceLevelAsync(baseForCalculationId);
        
        postModel.BaseForCalculationGuidId = baseForCalculationId;
        postModel.BaseForCalculationId = baseForCalculation.BaseForCalculationId;
        postModel.InheritanceLevelId = baseForCalculation.InheritanceLevel.Id;
        
        return await this.insertCommand.ExecuteAsync(postModel);
    }

    public async Task<IOperationResult<BaseForCalculationBasePayrollComponentModel>>
        PatchBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(
            Guid baseForCalculationBasePayrollComponentId,
            BaseForCalculationBasePayrollComponentPatchModel patchModel)
    {
        patchModel.Id = baseForCalculationBasePayrollComponentId;
        return await this.patchCommand.ExecuteAsync(patchModel);
    }

    public async Task<IOperationResult<NoResult>>
        DeleteBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(Guid baseForCalculationBasePayrollComponentId) =>
        await this.deleteCommand.ExecuteAsync(baseForCalculationBasePayrollComponentId);

    public async Task<IOperationResult<BaseForCalculationBasePayrollComponentMetadataModel>>
        GetBaseForCalculationBasePayrollComponentMetadataByProviderIdAsync(Guid providerId)
    {
        var metadata = new BaseForCalculationBasePayrollComponentMetadataModel
        {
            Origin = await this.codeTableHelper.GetOptions<CtBaseOrigin>()
        };

        return new OperationResult<BaseForCalculationBasePayrollComponentMetadataModel>(metadata);
    }

    public async Task<IListOperationResult<PayrollComponentMinimizedModel>>
        GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberAsync(
            Guid baseForCalculationId,
            int payrollPeriodNumber)
    {
        if (!await this.baseForCalculationBasePayrollComponentHelper.IsValidPayrollPeriodAsync(baseForCalculationId, payrollPeriodNumber))
        {
            return new ListOperationResult<PayrollComponentMinimizedModel>()
            { 
                Messages = new List<OperationMessage>(){ new (
                    messageType: MessageTypeEnum.BrokenBusinessRule, 
                    messageCode: MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollPeriodNumber_Invalid, 
                    description: "payrollPeriodNumber is invalid ")}
            };
        }
        return await this.getAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery.Execute(
            baseForCalculationId, payrollPeriodNumber);
    }
} 