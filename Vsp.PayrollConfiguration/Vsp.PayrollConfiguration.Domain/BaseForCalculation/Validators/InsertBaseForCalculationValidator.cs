using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;
using InheritanceLevelEnum = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculation.Validators;

internal class InsertBaseForCalculationValidator : AbstractValidator<BaseForCalculationPostModel>
{
    private readonly ILoketContext loketContext;

    public InsertBaseForCalculationValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;

        Include(new InsertInheritanceEntityValidator<BaseForCalculationPostModel, Repository.Entities.BaseForCalculation, ModelBaseForCalculation>(loketContext, mapper));

        RuleFor(x => x)
            .MustAsync(BeValidKey)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_Post_Key_ReservedCLA)
            .WithMessage("Base for calculations 16-20 are reserved for the CLA level.");
    }

    private async Task<bool> BeValidKey(BaseForCalculationPostModel model, CancellationToken cancellation)
    {
        if (model.Key is >= 16 and <= 20)
        {
            var inheritanceLevel = await this.loketContext.Set<InheritanceLevel>().AsNoTracking()
                .Include(x => x.InheritanceLevelInfo)
                .Where(x => x.Id == model.InheritanceLevelId)
                .SingleAsync(cancellation);
            return inheritanceLevel.InheritanceLevelInfo.Type == (int)InheritanceLevelEnum.CollectiveLaborAgreement;
        }

        return true;
    }
}