using Vsp.PayrollConfiguration.AbpFund.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.AbpFund;

[Collection(EntityNames.AbpFund)]
public class GetAbpFundsByYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "AbpFund";
    protected override bool UseTransaction => false;
    
    private static readonly Guid QA_AbpFund_GET_CLA_2025 = Guid.Parse("000009e5-07e9-0000-0000-000000000000");
    private static readonly Guid QA_AbpFund_GET_WM_2025 = Guid.Parse("000009e6-07e9-0000-0000-000000000000");
    private static readonly Guid QA_AbpFund_GET_PA_2025 = Guid.Parse("000009e7-07e9-0000-0000-000000000000");

    private const string OrderBy = "orderBy=startPayrollPeriod.periodNumber,key";
    private const string OrderByDesc = "orderBy=-startPayrollPeriod.periodNumber,-key";
    private const string FilterById = "filter=id eq '000009e7-07e9-0001-0200-000000000000'";
    private const string FilterByFranchiseUpToAge40Gt10 = "filter=franchiseUpToAge40 gt 10";

    [Fact]
    public async Task Ok_QA_AbpFund_GET_CLA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{AbpFundRoutes.GetAbpFundsByYearIdAsync}".Replace("{yearId:guid}", QA_AbpFund_GET_CLA_2025.ToString()),
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_QA_AbpFund_GET_WM_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{AbpFundRoutes.GetAbpFundsByYearIdAsync}".Replace("{yearId:guid}", QA_AbpFund_GET_WM_2025.ToString()),
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });
    
    [Fact]
    public async Task Ok_QA_AbpFund_GET_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{AbpFundRoutes.GetAbpFundsByYearIdAsync}".Replace("{yearId:guid}", QA_AbpFund_GET_PA_2025.ToString()),
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });
    
    [Fact]
    public async Task Ok_OrderBy_Period_QA_AbpFund_GET_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{AbpFundRoutes.GetAbpFundsByYearIdAsync}".Replace("{yearId:guid}", QA_AbpFund_GET_PA_2025.ToString()) + $"?{OrderBy}",
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });
    
    [Fact]
    public async Task Ok_OrderBy_Period_Desc_QA_AbpFund_GET_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{AbpFundRoutes.GetAbpFundsByYearIdAsync}".Replace("{yearId:guid}", QA_AbpFund_GET_PA_2025.ToString()) + $"?{OrderByDesc}",
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });
    
    [Fact]
    public async Task Ok_FilterById_QA_AbpFund_GET_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{AbpFundRoutes.GetAbpFundsByYearIdAsync}".Replace("{yearId:guid}", QA_AbpFund_GET_PA_2025.ToString()) + $"?{FilterById}",
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });
    
    [Fact]
    public async Task Ok_FilterByFranchiseUpToAge40_QA_AbpFund_GET_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{AbpFundRoutes.GetAbpFundsByYearIdAsync}".Replace("{yearId:guid}", QA_AbpFund_GET_PA_2025.ToString()) + $"?{FilterByFranchiseUpToAge40Gt10}",
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });
}