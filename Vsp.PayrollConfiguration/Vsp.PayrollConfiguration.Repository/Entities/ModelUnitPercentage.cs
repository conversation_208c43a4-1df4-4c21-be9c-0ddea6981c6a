namespace Vsp.PayrollConfiguration.Repository.Entities;

public class ModelUnitPercentage : GeneratedIdEntity, IInheritanceEntity, IPayrollPeriodEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int ComponentId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int PayrollPeriodId { get; set; }

    public decimal? Percentage { get; set; }
    public int? CalculateOver { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public VwPayrollPeriod VwPayrollPeriod { get; set; } = null!;
    public VwComponent VwComponent { get; set; } = null!;
    public CtCalculateOver? CtCalculateOver { get; set; }
}
