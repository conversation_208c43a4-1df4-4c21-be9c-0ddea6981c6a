namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ModelComponentDeviantConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelComponentDeviant>
{
    public override void Configure(EntityTypeBuilder<ModelComponentDeviant> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelComponentAfwijkend", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.InheritanceLevelId, x.ComponentId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");
        builder.Property(x => x.RouteType).HasColumnName("SoortRoute");

        builder.HasOne(x => x.CtRouteType).WithMany().HasForeign<PERSON>ey(x => x.RouteType);
    }
}