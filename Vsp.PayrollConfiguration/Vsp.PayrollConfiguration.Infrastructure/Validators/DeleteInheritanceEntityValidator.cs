using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Infrastructure.Validators;

public class DeleteInheritanceEntityValidator<TModelEntity> : AbstractValidator<TModelEntity>
    where TModelEntity : GeneratedIdEntity, IInheritanceEntity, new()
{
    private readonly ILoketContext loketContext;
    private readonly IMapper mapper;

    public DeleteInheritanceEntityValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;
        this.mapper = mapper;

        this.RuleLevelCascadeMode = CascadeMode.Stop;

        WhenAsync((modelEntity, token) => EntityIsDefinedOnCurrentInheritanceLevelAsync(modelEntity, loketContext, mapper, token), () =>
        {
            RuleFor(obj => obj)
                .MustAsync(InheritanceLevelDoesNotHaveChildInheritanceLevelAsync).WithErrorCode(MessageCodes.API_PayrollConfiguration_Delete_EntityHasChildren)
                .WithMessage("Cannot delete entities from current inheritance level, because it has dependent child inheritance level(s).")
                .MustAsync(FirstPayrollPeriodAlwaysExistsAsync)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted)
                .WithMessage("Cannot delete data for the first payroll period in this year, because there is still data for later payroll period(s).");
        });
    }

    public static async Task<bool> EntityIsDefinedOnCurrentInheritanceLevelAsync(TModelEntity modelEntity, ILoketContext loketContext, IMapper mapper, CancellationToken token)
    {
        // Does the entity exist on the current level?
        var entityExistsOnCurrentLevel = await loketContext.Set<TModelEntity>()
            .Where(GeneratedIdHelper.ConstructWhere<TModelEntity>(modelEntity.Id))
            .AnyAsync(token);
        if (!entityExistsOnCurrentLevel) return false;

        // Determine parent and grandparent inheritance levels.
        var inheritanceLevelInfo = await loketContext.Set<InheritanceLevelInfo>()
            .Where(ili => ili.InheritanceLevelId == modelEntity.InheritanceLevelId)
            .SingleAsync(token);

        // Does the entity exist on the parent level? If so, this is an override, which may be deleted freely.
        if (inheritanceLevelInfo.ParentInheritanceLevelId == 0) return true;

        var entityCopy = mapper.Map<TModelEntity>(modelEntity);
        if (ReferenceEquals(modelEntity, entityCopy))
        {
            throw new InvalidOperationException($"Missing type map for cloning: {typeof(TModelEntity).Name}");
        }

        entityCopy.InheritanceLevelId = inheritanceLevelInfo.ParentInheritanceLevelId;
        GeneratedIdHelper.GenerateId(entityCopy); // Force ID re-generation because of new InheritanceLevelId

        var entityExistsOnParentLevel = await loketContext.Set<TModelEntity>()
            .Where(GeneratedIdHelper.ConstructWhere<TModelEntity>(entityCopy.Id))
            .AnyAsync(token);
        if (entityExistsOnParentLevel) return false;

        // Does the entity exist on the grandparent level? If so, this is an override, which may be deleted freely.
        if (inheritanceLevelInfo.GrandParentInheritanceLevelId == 0) return true;

        entityCopy.InheritanceLevelId = inheritanceLevelInfo.GrandParentInheritanceLevelId;
        GeneratedIdHelper.GenerateId(entityCopy); // Force ID re-generation because of new InheritanceLevelId

        var entityExistsOnGrandParentLevel = await loketContext.Set<TModelEntity>()
            .Where(GeneratedIdHelper.ConstructWhere<TModelEntity>(entityCopy.Id))
            .AnyAsync(token);
        return !entityExistsOnGrandParentLevel;
    }

    private async Task<bool> InheritanceLevelDoesNotHaveChildInheritanceLevelAsync(TModelEntity modelEntity, CancellationToken token)
    {
        var childrenExists = await this.loketContext.Set<InheritanceLevel>()
            .Where(mw => mw.ParentInheritanceLevelId == modelEntity.InheritanceLevelId)
            .AnyAsync(token);
        return !childrenExists;
    }

    private async Task<bool> FirstPayrollPeriodAlwaysExistsAsync(TModelEntity modelEntity, CancellationToken token)
    {
        if (modelEntity is IPayrollPeriodEntity payrollPeriodEntity)
        {
            // Entities after the first payroll period may always be deleted.
            if (payrollPeriodEntity.PayrollPeriodId > 1) return true;

            // If there are no other entities after the first payroll period on the current level, the first payroll period may always be deleted.
            var afterFirstPayrollPeriodEntitiesExistOnCurrentLevel = await this.loketContext.Set<TModelEntity>()
                .Where(GeneratedIdHelper.ConstructWhere<TModelEntity>(payrollPeriodEntity.Id, new() { { nameof(payrollPeriodEntity.PayrollPeriodId), GeneratedIdKeyComparison.GreaterThan } }))
                .AnyAsync(token);
            return !afterFirstPayrollPeriodEntitiesExistOnCurrentLevel;
        }

        return true;
    }
}