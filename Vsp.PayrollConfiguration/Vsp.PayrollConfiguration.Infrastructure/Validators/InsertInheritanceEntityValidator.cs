using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;
using Vsp.PayrollConfiguration.Repository.Entities;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Infrastructure.Validators;

public class InsertInheritanceEntityValidator<TPostModel, TEntity, TModelEntity> : InitializableAbstractValidator<TPostModel, InsertInheritanceEntityValidationInfo>
    where TPostModel : class, IPostModel
    where TEntity : GeneratedIdEntity, IInheritanceEntity, new()
    where TModelEntity : GeneratedIdEntity, IInheritanceEntity, new()
{
    private readonly ILoketContext loketContext;
    private readonly IMapper mapper;

    public InsertInheritanceEntityValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;
        this.mapper = mapper;

        this.RuleLevelCascadeMode = CascadeMode.Stop;

        RuleFor(obj => obj)
            .MustAsync(BeValidYearAsync)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_Insert_Year_DoesNotExist)
                .WithMessage("Year does not exist on current inheritance level.")
            .MustAsync(BeValidPayrollPeriodAsync)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist)
                .WithMessage("Payroll period does not exist on current inheritance level.")
            .MustAsync(FirstPayrollPeriodAlwaysExistsAsync)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_FirstPeriodDoesNotExist)
                .WithMessage("Cannot add data for a later payroll period in this year, because there is no data for the first payroll period yet.")
            .MustAsync(BeNonExistingOnCurrentInheritanceLevelAsync)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_Insert_Entity_AlreadyExists_CurrentInheritanceLevel)
                .WithMessage("Entity already exists on current inheritance level.")
            .MustAsync(BeNonExistingOnParentInheritanceLevelAsync)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_Insert_Entity_AlreadyExists_ParentInheritanceLevel)
                .WithMessage("Entity already exists on parent inheritance level.");
    }

    private async Task<bool> BeValidYearAsync(TPostModel postModel, CancellationToken token)
    {
        if (this.ValidationInformation.InheritanceLevel == null) return true;

        if (postModel is IYearPostModel yearPostModel)
        {
            var modelEntity = this.mapper.Map<TModelEntity>(postModel, opt => opt.Items[nameof(IInheritanceEntity.InheritanceLevelId)] = this.ValidationInformation.InheritanceLevel.InheritanceLevelId);

            if (modelEntity is IYearEntity yearEntity)
            {
                var yearExists = await this.loketContext.Set<Year>()
                    .Where(y => y.InheritanceLevelId == yearEntity.InheritanceLevelId && y.YearId == yearEntity.YearId)
                    .AnyAsync(token);
                return yearExists;
            }
        }
        return true;
    }

    private async Task<bool> BeValidPayrollPeriodAsync(TPostModel postModel, CancellationToken token)
    {
        if (this.ValidationInformation.InheritanceLevel == null) return true;

        if (postModel is IPayrollPeriodPostModel payrollPeriodPostModel)
        {
            var modelEntity = this.mapper.Map<TModelEntity>(postModel, opt => opt.Items[nameof(IInheritanceEntity.InheritanceLevelId)] = this.ValidationInformation.InheritanceLevel.InheritanceLevelId);

            if (modelEntity is IPayrollPeriodEntity payrollPeriodEntity)
            {
                var payrollPeriodExists = await this.loketContext.Set<PayrollPeriod>()
                    .Where(pp => pp.InheritanceLevelId == payrollPeriodEntity.InheritanceLevelId && pp.YearId == payrollPeriodEntity.YearId && pp.PayrollPeriodId == payrollPeriodEntity.PayrollPeriodId)
                    .AnyAsync(token);
                return payrollPeriodExists;
            }
        }
        return true;
    }

    private async Task<bool> FirstPayrollPeriodAlwaysExistsAsync(TPostModel postModel, CancellationToken token)
    {
        if (this.ValidationInformation.InheritanceLevel == null) return true;

        if (postModel is IPayrollPeriodPostModel payrollPeriodPostModel)
        {
            // First payroll period given, no need to check if there is an entity for the first payroll period.
            if (payrollPeriodPostModel.StartPayrollPeriod.PeriodNumber == 1) return true;

            var modelEntity = this.mapper.Map<TModelEntity>(postModel, opt => opt.Items[nameof(IInheritanceEntity.InheritanceLevelId)] = this.ValidationInformation.InheritanceLevel.InheritanceLevelId);

            if (modelEntity is IPayrollPeriodEntity payrollPeriodEntity)
            {
                // Check if there is data for the first payroll period, I.E. does an entity already exist that has payroll period number 1?
                payrollPeriodEntity.PayrollPeriodId = 1;

                var firstPayrollPeriodEntityExists = await this.loketContext.Set<TEntity>()
                    .Where(GeneratedIdHelper.ConstructWhere<TEntity>(payrollPeriodEntity.Id))
                    .AnyAsync(token);
                return firstPayrollPeriodEntityExists;
            }
        }
        return true;
    }

    private async Task<bool> BeNonExistingOnCurrentInheritanceLevelAsync(TPostModel postModel, CancellationToken token)
    {
        if (this.ValidationInformation.InheritanceLevel == null) return true;

        var modelEntity = this.mapper.Map<TModelEntity>(postModel, opt => opt.Items[nameof(IInheritanceEntity.InheritanceLevelId)] = this.ValidationInformation.InheritanceLevel.InheritanceLevelId);

        var entityExistsOnCurrentLevel = await this.loketContext.Set<TModelEntity>()
            .Where(GeneratedIdHelper.ConstructWhere<TModelEntity>(modelEntity.Id))
            .AnyAsync(token);
        return !entityExistsOnCurrentLevel;
    }

    private async Task<bool> BeNonExistingOnParentInheritanceLevelAsync(TPostModel postModel, CancellationToken token)
    {
        if (this.ValidationInformation.InheritanceLevel?.ParentInheritanceLevel == null) return true;

        var parentEntity = this.mapper.Map<TModelEntity>(postModel, opt => opt.Items[nameof(IInheritanceEntity.InheritanceLevelId)] = this.ValidationInformation.InheritanceLevel.ParentInheritanceLevel.InheritanceLevelId);

        var entityExistsOnParentLevel = await this.loketContext.Set<TEntity>()
            .Where(GeneratedIdHelper.ConstructWhere<TEntity>(parentEntity.Id)) // NOTE: We use the ID of the model entity to retrieve the entity (these are the same).
            .AnyAsync(token);
        return !entityExistsOnParentLevel;
    }

    protected override async Task<InsertInheritanceEntityValidationInfo> Initialize(TPostModel obj)
    {
        var inheritanceLevel = await this.loketContext.Set<InheritanceLevel>()
            .Include(x => x.ParentInheritanceLevel)
            .Where(x => x.Id == obj.InheritanceLevelId)
            .SingleOrDefaultAsync();

        return new InsertInheritanceEntityValidationInfo
        {
            InheritanceLevel = inheritanceLevel,
        };
    }
}

public class InsertInheritanceEntityValidationInfo
{
    public InheritanceLevel? InheritanceLevel { get; set; }
}
